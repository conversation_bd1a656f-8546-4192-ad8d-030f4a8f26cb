{"logs": [{"outputFile": "com.example.salati.app-mergeDebugResources-41:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2872,2975,3079,3182,3284,3389,3495,6450", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "2970,3074,3177,3279,3384,3490,3609,6546"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/845d779a6f9703b804f5e43452bcc2e5/transformed/appcompat-1.1.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,81,90,91,94,93,100,92,94,93,90,90,83,114,108,98,125,106,107,159,102,84", "endOffsets": "213,320,436,523,632,755,837,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1940,2055,2164,2263,2389,2496,2604,2764,2867,2952"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1945,2060,2169,2268,2394,2501,2609,2769,6365", "endColumns": "112,106,115,86,108,122,81,81,90,91,94,93,100,92,94,93,90,90,83,114,108,98,125,106,107,159,102,84", "endOffsets": "213,320,436,523,632,755,837,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1940,2055,2164,2263,2389,2496,2604,2764,2867,6445"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/12a085f4af97d8500c9432a48145ea1b/transformed/preference-1.2.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,270,350,502,671,752", "endColumns": "75,88,79,151,168,80,78", "endOffsets": "176,265,345,497,666,747,826"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5968,6044,6133,6213,6551,6720,6801", "endColumns": "75,88,79,151,168,80,78", "endOffsets": "6039,6128,6208,6360,6715,6796,6875"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values-my/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3614,3721,3885,4019,4130,4277,4409,4532,4796,4972,5078,5248,5391,5549,5736,5806,5879", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "3716,3880,4014,4125,4272,4404,4527,4637,4967,5073,5243,5386,5544,5731,5801,5874,5963"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values-my/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4642", "endColumns": "153", "endOffsets": "4791"}}]}]}