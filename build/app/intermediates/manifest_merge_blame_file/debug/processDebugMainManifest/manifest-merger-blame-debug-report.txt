1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.salati"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:7:5-67
15-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:7:22-64
16    <!-- Location permissions -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:3:5-79
17-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:3:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Notification permissions -->
18-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:4:5-81
18-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:10:5-81
19-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:11:5-66
20-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:11:22-63
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:12:5-68
21-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:12:22-65
22    <!--
23 Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:51:5-56:15
30        <intent>
30-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:52:9-55:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:53:13-72
31-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:53:21-70
32
33            <data android:mimeType="text/plain" />
33-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:54:13-50
33-->/Users/<USER>/Documents/augment-projects/Salati/android/app/src/main/AndroidManifest.xml:54:19-48
34        </intent>
35    </queries>
36
37    <permission
37-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
38        android:name="com.example.salati.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.salati.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="true"
48        android:icon="@mipmap/ic_launcher"
49        android:label="salati" >
50        <activity
51            android:name="com.example.salati.MainActivity"
52            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
53            android:exported="true"
54            android:hardwareAccelerated="true"
55            android:launchMode="singleTop"
56            android:taskAffinity=""
57            android:theme="@style/LaunchTheme"
58            android:windowSoftInputMode="adjustResize" >
59
60            <!--
61                 Specifies an Android theme to apply to this Activity as soon as
62                 the Android process has started. This theme is visible to the user
63                 while the Flutter UI initializes. After that, this theme continues
64                 to determine the Window background behind the Flutter UI.
65            -->
66            <meta-data
67                android:name="io.flutter.embedding.android.NormalTheme"
68                android:resource="@style/NormalTheme" />
69
70            <intent-filter>
71                <action android:name="android.intent.action.MAIN" />
72
73                <category android:name="android.intent.category.LAUNCHER" />
74            </intent-filter>
75        </activity>
76        <!--
77             Don't delete the meta-data below.
78             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
79        -->
80        <meta-data
81            android:name="flutterEmbedding"
82            android:value="2" />
83
84        <service
84-->[:geolocator_android] /Users/<USER>/Documents/augment-projects/Salati/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:56
85            android:name="com.baseflow.geolocator.GeolocatorLocationService"
85-->[:geolocator_android] /Users/<USER>/Documents/augment-projects/Salati/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-77
86            android:enabled="true"
86-->[:geolocator_android] /Users/<USER>/Documents/augment-projects/Salati/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-35
87            android:exported="false"
87-->[:geolocator_android] /Users/<USER>/Documents/augment-projects/Salati/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
88            android:foregroundServiceType="location" />
88-->[:geolocator_android] /Users/<USER>/Documents/augment-projects/Salati/build/geolocator_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-53
89
90        <uses-library
90-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
91            android:name="androidx.window.extensions"
91-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
92            android:required="false" />
92-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
93        <uses-library
93-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
94            android:name="androidx.window.sidecar"
94-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
95            android:required="false" />
95-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
96
97        <activity
97-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
98            android:name="com.google.android.gms.common.api.GoogleApiActivity"
98-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:19-85
99            android:exported="false"
99-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:22:19-43
100            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
100-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:21:19-78
101
102        <meta-data
102-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
103            android:name="com.google.android.gms.version"
103-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
104            android:value="@integer/google_play_services_version" />
104-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
105
106        <provider
106-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
108            android:authorities="com.example.salati.androidx-startup"
108-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
114                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
115                android:value="androidx.startup" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
116        </provider>
117
118        <receiver
118-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
119            android:name="androidx.profileinstaller.ProfileInstallReceiver"
119-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
120            android:directBootAware="false"
120-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
121            android:enabled="true"
121-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
122            android:exported="true"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
123            android:permission="android.permission.DUMP" >
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
125                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
125-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
128                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
128-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
131                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
131-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
134                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
134-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
135            </intent-filter>
136        </receiver>
137    </application>
138
139</manifest>
