[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86_64", "file_": "/Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Documents/augment-projects/Salati/android/app/.cxx/Debug/2v284ur2/x86_64/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/src/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt", "tag_": "profile|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]