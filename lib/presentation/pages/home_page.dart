import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/constants/app_constants.dart';
import '../../features/location/presentation/controllers/location_controller.dart';
import '../../features/prayer_times/presentation/controllers/prayer_times_controller.dart';
import '../../features/prayer_times/domain/entities/prayer_times_entity.dart';

/// Home page of the Salati app
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final locationController = Get.find<LocationController>();
    final prayerTimesController = Get.find<PrayerTimesController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.mosque,
                      size: 48,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'مرحباً بك في تطبيق صلاتي',
                      style: Theme.of(context).textTheme.headlineSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'تطبيق تذكير أوقات الصلاة',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Location Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الموقع الحالي',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    Obx(() {
                      if (locationController.isLoading) {
                        return const Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 12),
                            Text('جاري تحديد الموقع...'),
                          ],
                        );
                      }
                      
                      if (locationController.hasLocation) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              locationController.locationString,
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              locationController.coordinatesString,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        );
                      }
                      
                      if (locationController.errorMessage.isNotEmpty) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'خطأ في تحديد الموقع',
                              style: TextStyle(color: Colors.red[700]),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              locationController.errorMessage,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        );
                      }
                      
                      return const Text('الموقع غير متوفر');
                    }),
                    
                    const SizedBox(height: 12),
                    
                    ElevatedButton.icon(
                      onPressed: () => locationController.refreshLocation(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('تحديث الموقع'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Prayer Times Section
            _buildPrayerTimesSection(context, prayerTimesController),
            
            const Spacer(),
            
            // Status Information
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'Sprint 2: جلب أوقات الصلاة',
                    style: Theme.of(context).textTheme.titleSmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'تم إنجاز: Prayer Times API، Models، Repository، Controller، UI',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build prayer times section widget
  Widget _buildPrayerTimesSection(BuildContext context, PrayerTimesController controller) {
    return Obx(() {
      if (controller.isLoading) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل أوقات الصلاة...',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        );
      }

      if (controller.hasError) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red[700],
                ),
                const SizedBox(height: 8),
                Text(
                  'خطأ في تحميل أوقات الصلاة',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  controller.errorMessage,
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => controller.refreshPrayerTimes(forceRefresh: true),
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        );
      }

      if (!controller.hasPrayerTimes) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(
                  Icons.access_time,
                  size: 48,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(height: 8),
                Text(
                  'أوقات الصلاة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'لا توجد بيانات متاحة',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => controller.refreshPrayerTimes(),
                  icon: const Icon(Icons.download),
                  label: const Text('تحميل أوقات الصلاة'),
                ),
              ],
            ),
          ),
        );
      }

      // Display prayer times
      return Column(
        children: [
          // Next Prayer Countdown Card
          _buildNextPrayerCard(context, controller),
          const SizedBox(height: 16),
          // Prayer Times List
          _buildPrayerTimesList(context, controller),
        ],
      );
    });
  }

  /// Build next prayer countdown card
  Widget _buildNextPrayerCard(BuildContext context, PrayerTimesController controller) {
    return Card(
      elevation: 6,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Text(
              'الصلاة التالية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.nextPrayerName,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                controller.timeUntilNextPrayerString,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'الوقت المتبقي',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build prayer times list
  Widget _buildPrayerTimesList(BuildContext context, PrayerTimesController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'أوقات الصلاة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  onPressed: () => controller.refreshPrayerTimes(forceRefresh: true),
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث',
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...PrayerType.values.map((prayerType) {
              final isNext = controller.nextPrayer == prayerType;
              final isCurrent = controller.currentPrayer == prayerType;

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isNext
                      ? Theme.of(context).primaryColor.withOpacity(0.1)
                      : isCurrent
                          ? Colors.green.withOpacity(0.1)
                          : null,
                  borderRadius: BorderRadius.circular(8),
                  border: isNext
                      ? Border.all(color: Theme.of(context).primaryColor, width: 2)
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getPrayerIcon(prayerType),
                          color: isNext
                              ? Theme.of(context).primaryColor
                              : isCurrent
                                  ? Colors.green
                                  : Colors.grey[600],
                        ),
                        const SizedBox(width: 12),
                        Text(
                          prayerType.arabicName,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: isNext ? FontWeight.bold : FontWeight.normal,
                            color: isNext
                                ? Theme.of(context).primaryColor
                                : null,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      controller.getPrayerTimeString(prayerType),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: isNext ? FontWeight.bold : FontWeight.normal,
                        fontFamily: 'monospace',
                        color: isNext
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
            if (controller.prayerTimes != null) ...[
              Divider(color: Colors.grey[300]),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'التاريخ الهجري',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    controller.prayerTimes!.hijriDate,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get icon for prayer type
  IconData _getPrayerIcon(PrayerType prayerType) {
    switch (prayerType) {
      case PrayerType.fajr:
        return Icons.wb_twilight;
      case PrayerType.dhuhr:
        return Icons.wb_sunny;
      case PrayerType.asr:
        return Icons.wb_sunny_outlined;
      case PrayerType.maghrib:
        return Icons.wb_twilight;
      case PrayerType.isha:
        return Icons.nightlight_round;
    }
  }
}
