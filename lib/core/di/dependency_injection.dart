import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../network/dio_client.dart';
import '../network/network_info.dart';
import '../../features/location/data/datasources/location_local_data_source.dart';
import '../../features/location/data/datasources/location_remote_data_source.dart';
import '../../features/location/data/repositories/location_repository_impl.dart';
import '../../features/location/domain/repositories/location_repository.dart';
import '../../features/location/domain/usecases/get_current_location.dart';
import '../../features/location/presentation/controllers/location_controller.dart';

/// Dependency injection setup for the app
class DependencyInjection {
  /// Initialize all dependencies
  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(sharedPreferences);

    // Core dependencies
    Get.put<DioClient>(DioClient());
    Get.put<NetworkInfo>(NetworkInfoImpl());

    // Location feature dependencies
    _initLocationDependencies();
  }

  /// Initialize location feature dependencies
  static void _initLocationDependencies() {
    // Data sources
    Get.put<LocationLocalDataSource>(
      LocationLocalDataSourceImpl(
        sharedPreferences: Get.find<SharedPreferences>(),
      ),
    );

    Get.put<LocationRemoteDataSource>(
      LocationRemoteDataSourceImpl(),
    );

    // Repository
    Get.put<LocationRepository>(
      LocationRepositoryImpl(
        remoteDataSource: Get.find<LocationRemoteDataSource>(),
        localDataSource: Get.find<LocationLocalDataSource>(),
      ),
    );

    // Use cases
    Get.put<GetCurrentLocation>(
      GetCurrentLocation(Get.find<LocationRepository>()),
    );

    // Controllers
    Get.put<LocationController>(
      LocationController(
        getCurrentLocationUseCase: Get.find<GetCurrentLocation>(),
      ),
    );
  }

  /// Initialize prayer times dependencies (to be implemented in Sprint 2)
  static void _initPrayerTimesDependencies() {
    // TODO: Implement in Sprint 2
  }

  /// Initialize notifications dependencies (to be implemented in Sprint 3)
  static void _initNotificationsDependencies() {
    // TODO: Implement in Sprint 3
  }

  /// Initialize settings dependencies (to be implemented in Sprint 5)
  static void _initSettingsDependencies() {
    // TODO: Implement in Sprint 5
  }
}
