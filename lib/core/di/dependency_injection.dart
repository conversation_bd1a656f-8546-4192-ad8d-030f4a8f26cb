import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../network/dio_client.dart';
import '../network/network_info.dart';
import '../../features/location/data/datasources/location_local_data_source.dart';
import '../../features/location/data/datasources/location_remote_data_source.dart';
import '../../features/location/data/repositories/location_repository_impl.dart';
import '../../features/location/domain/repositories/location_repository.dart';
import '../../features/location/domain/usecases/get_current_location.dart';
import '../../features/location/presentation/controllers/location_controller.dart';
import '../../features/prayer_times/data/datasources/prayer_times_local_data_source.dart';
import '../../features/prayer_times/data/datasources/prayer_times_remote_data_source.dart';
import '../../features/prayer_times/data/repositories/prayer_times_repository_impl.dart';
import '../../features/prayer_times/domain/repositories/prayer_times_repository.dart';
import '../../features/prayer_times/domain/usecases/get_prayer_times.dart';
import '../../features/prayer_times/presentation/controllers/prayer_times_controller.dart';

/// Dependency injection setup for the app
class DependencyInjection {
  /// Initialize all dependencies
  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(sharedPreferences);

    // Core dependencies
    Get.put<DioClient>(DioClient());
    Get.put<NetworkInfo>(NetworkInfoImpl());

    // Location feature dependencies
    _initLocationDependencies();

    // Prayer times feature dependencies
    _initPrayerTimesDependencies();
  }

  /// Initialize location feature dependencies
  static void _initLocationDependencies() {
    // Data sources
    Get.put<LocationLocalDataSource>(
      LocationLocalDataSourceImpl(
        sharedPreferences: Get.find<SharedPreferences>(),
      ),
    );

    Get.put<LocationRemoteDataSource>(
      LocationRemoteDataSourceImpl(),
    );

    // Repository
    Get.put<LocationRepository>(
      LocationRepositoryImpl(
        remoteDataSource: Get.find<LocationRemoteDataSource>(),
        localDataSource: Get.find<LocationLocalDataSource>(),
      ),
    );

    // Use cases
    Get.put<GetCurrentLocation>(
      GetCurrentLocation(Get.find<LocationRepository>()),
    );

    // Controllers
    Get.put<LocationController>(
      LocationController(
        getCurrentLocationUseCase: Get.find<GetCurrentLocation>(),
      ),
    );
  }

  /// Initialize prayer times dependencies
  static void _initPrayerTimesDependencies() {
    // Data sources
    Get.put<PrayerTimesLocalDataSource>(
      PrayerTimesLocalDataSourceImpl(
        sharedPreferences: Get.find<SharedPreferences>(),
      ),
    );

    Get.put<PrayerTimesRemoteDataSource>(
      PrayerTimesRemoteDataSourceImpl(
        dioClient: Get.find<DioClient>(),
      ),
    );

    // Repository
    Get.put<PrayerTimesRepository>(
      PrayerTimesRepositoryImpl(
        remoteDataSource: Get.find<PrayerTimesRemoteDataSource>(),
        localDataSource: Get.find<PrayerTimesLocalDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    // Use cases
    Get.put<GetPrayerTimes>(
      GetPrayerTimes(Get.find<PrayerTimesRepository>()),
    );

    // Controllers
    Get.put<PrayerTimesController>(
      PrayerTimesController(
        getPrayerTimesUseCase: Get.find<GetPrayerTimes>(),
      ),
    );
  }

  /// Initialize notifications dependencies (to be implemented in Sprint 3)
  static void _initNotificationsDependencies() {
    // TODO: Implement in Sprint 3
  }

  /// Initialize settings dependencies (to be implemented in Sprint 5)
  static void _initSettingsDependencies() {
    // TODO: Implement in Sprint 5
  }
}
