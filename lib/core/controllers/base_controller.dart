import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Base controller class with common functionality
abstract class BaseController extends GetxController {
  // Loading state
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  // Error handling
  final _errorMessage = ''.obs;
  String get errorMessage => _errorMessage.value;
  bool get hasError => _errorMessage.value.isNotEmpty;

  // Success message
  final _successMessage = ''.obs;
  String get successMessage => _successMessage.value;
  bool get hasSuccess => _successMessage.value.isNotEmpty;

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// Set error message
  void setError(String message) {
    _errorMessage.value = message;
    _successMessage.value = '';
  }

  /// Set success message
  void setSuccess(String message) {
    _successMessage.value = message;
    _errorMessage.value = '';
  }

  /// Clear all messages
  void clearMessages() {
    _errorMessage.value = '';
    _successMessage.value = '';
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Clear success message
  void clearSuccess() {
    _successMessage.value = '';
  }

  /// Show loading dialog
  void showLoadingDialog({String? message}) {
    Get.dialog(
      PopScope(
        canPop: false,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Get.theme.cardColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(message),
                ],
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// Hide loading dialog
  void hideLoadingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Show error snackbar
  void showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error,
      colorText: Get.theme.colorScheme.onError,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show success snackbar
  void showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show info snackbar
  void showInfoSnackbar(String message) {
    Get.snackbar(
      'معلومات',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.surface,
      colorText: Get.theme.colorScheme.onSurface,
      duration: const Duration(seconds: 3),
    );
  }

  /// Handle async operations with loading and error handling
  Future<T?> handleAsyncOperation<T>(
    Future<T> Function() operation, {
    String? loadingMessage,
    String? successMessage,
    bool showLoading = true,
    bool showSuccess = false,
  }) async {
    try {
      if (showLoading) {
        setLoading(true);
        if (loadingMessage != null) {
          showLoadingDialog(message: loadingMessage);
        }
      }

      clearMessages();
      final result = await operation();

      if (successMessage != null) {
        setSuccess(successMessage);
        if (showSuccess) {
          showSuccessSnackbar(successMessage);
        }
      }

      return result;
    } catch (e) {
      final errorMsg = e.toString();
      setError(errorMsg);
      showErrorSnackbar(errorMsg);
      return null;
    } finally {
      if (showLoading) {
        setLoading(false);
        if (loadingMessage != null) {
          hideLoadingDialog();
        }
      }
    }
  }

  @override
  void onClose() {
    // Clean up resources
    clearMessages();
    super.onClose();
  }
}
