/// App Constants for Salati Prayer Reminder App
class AppConstants {
  // App Information
  static const String appName = 'صلاتي - Salati';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Islamic Prayer Reminder App';

  // Colors - Islamic Theme
  static const String primaryColorHex = '#2E8B57'; // Emerald Green
  static const String secondaryColorHex = '#FFD700'; // Gold
  static const String backgroundColorHex = '#F5F5F5';
  static const String textColorHex = '#333333';

  // API Configuration
  static const String baseApiUrl = 'http://api.aladhan.com/v1';
  static const String prayerTimesEndpoint = '/timings';
  static const String qiblaEndpoint = '/qibla';

  // Prayer Names
  static const List<String> prayerNamesArabic = [
    'الفجر',
    'الظهر', 
    'العصر',
    'المغرب',
    'العشاء'
  ];

  static const List<String> prayerNamesEnglish = [
    'Fajr',
    'Dhuhr',
    'Asr', 
    'Maghrib',
    'Isha'
  ];

  // Storage Keys
  static const String locationLatKey = 'location_latitude';
  static const String locationLngKey = 'location_longitude';
  static const String locationCityKey = 'location_city';
  static const String settingsKey = 'app_settings';
  static const String prayerTimesKey = 'prayer_times';
  static const String lastUpdateKey = 'last_update';

  // Notification IDs
  static const int fajrNotificationId = 1;
  static const int dhuhrNotificationId = 2;
  static const int asrNotificationId = 3;
  static const int maghribNotificationId = 4;
  static const int ishaNotificationId = 5;

  // Wudu Notification IDs (offset by 10)
  static const int fajrWuduNotificationId = 11;
  static const int dhuhrWuduNotificationId = 12;
  static const int asrWuduNotificationId = 13;
  static const int maghribWuduNotificationId = 14;
  static const int ishaWuduNotificationId = 15;

  // Default Settings
  static const int defaultWuduReminderMinutes = 15;
  static const String defaultLanguage = 'ar';
  static const int defaultCalculationMethod = 2; // Islamic Society of North America
  static const bool defaultNotificationsEnabled = true;
  static const double defaultVolume = 0.8;

  // Calculation Methods
  static const Map<int, String> calculationMethods = {
    1: 'University of Islamic Sciences, Karachi',
    2: 'Islamic Society of North America',
    3: 'Muslim World League',
    4: 'Umm Al-Qura University, Makkah',
    5: 'Egyptian General Authority of Survey',
    7: 'Institute of Geophysics, University of Tehran',
    8: 'Gulf Region',
    9: 'Kuwait',
    10: 'Qatar',
    11: 'Majlis Ugama Islam Singapura, Singapore',
    12: 'Union Organization islamic de France',
    13: 'Diyanet İşleri Başkanlığı, Turkey',
    14: 'Spiritual Administration of Muslims of Russia'
  };

  // Error Messages
  static const String locationPermissionDenied = 'Location permission denied';
  static const String locationServiceDisabled = 'Location service disabled';
  static const String networkError = 'Network connection error';
  static const String apiError = 'API request failed';
  static const String unknownError = 'Unknown error occurred';

  // Success Messages
  static const String locationUpdated = 'Location updated successfully';
  static const String settingsSaved = 'Settings saved successfully';
  static const String notificationScheduled = 'Notifications scheduled';

  // File Paths
  static const String adhanAudioPath = 'assets/audio/adhan.mp3';
  static const String notificationSoundPath = 'assets/audio/notification.mp3';
  
  // Assets
  static const String logoPath = 'assets/images/logo.png';
  static const String qiblaCompassPath = 'assets/images/qibla_compass.png';
  static const String islamicPatternPath = 'assets/images/islamic_pattern.png';

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 300);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);

  // Refresh Intervals
  static const Duration prayerTimesRefreshInterval = Duration(hours: 24);
  static const Duration locationRefreshInterval = Duration(hours: 1);
  static const Duration uiRefreshInterval = Duration(seconds: 1);
}
