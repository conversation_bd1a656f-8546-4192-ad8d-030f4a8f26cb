import 'package:dio/dio.dart';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';

/// Dio client configuration for API calls
class DioClient {
  late final Dio _dio;

  DioClient() {
    _dio = Dio();
    _configureDio();
  }

  /// Configure Dio with interceptors and options
  void _configureDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConstants.baseApiUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add interceptors
    _dio.interceptors.add(_createLoggingInterceptor());
    _dio.interceptors.add(_createErrorInterceptor());
  }

  /// Create logging interceptor for debugging
  Interceptor _createLoggingInterceptor() {
    return LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: false,
      error: true,
      logPrint: (object) {
        // In production, you might want to use a proper logging library
        print('DIO: $object');
      },
    );
  }

  /// Create error interceptor for handling common errors
  Interceptor _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        final exception = _handleDioError(error);
        handler.reject(DioException(
          requestOptions: error.requestOptions,
          error: exception,
          type: error.type,
          response: error.response,
        ));
      },
    );
  }

  /// Handle Dio errors and convert to app exceptions
  AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout. Please check your internet connection.',
        );

      case DioExceptionType.badResponse:
        return _handleResponseError(error.response);

      case DioExceptionType.cancel:
        return const NetworkException(
          message: 'Request was cancelled.',
        );

      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'No internet connection. Please check your network settings.',
        );

      case DioExceptionType.badCertificate:
        return const NetworkException(
          message: 'Certificate verification failed.',
        );

      case DioExceptionType.unknown:
      default:
        return NetworkException(
          message: 'Network error: ${error.message}',
        );
    }
  }

  /// Handle HTTP response errors
  AppException _handleResponseError(Response? response) {
    if (response == null) {
      return const ServerException(
        message: 'No response from server.',
      );
    }

    switch (response.statusCode) {
      case 400:
        return ServerException(
          message: 'Bad request: ${_extractErrorMessage(response)}',
          code: 400,
        );
      case 401:
        return const ServerException(
          message: 'Unauthorized access.',
          code: 401,
        );
      case 403:
        return const ServerException(
          message: 'Access forbidden.',
          code: 403,
        );
      case 404:
        return const ServerException(
          message: 'Resource not found.',
          code: 404,
        );
      case 500:
        return const ServerException(
          message: 'Internal server error.',
          code: 500,
        );
      case 502:
        return const ServerException(
          message: 'Bad gateway.',
          code: 502,
        );
      case 503:
        return const ServerException(
          message: 'Service unavailable.',
          code: 503,
        );
      default:
        return ServerException(
          message: 'Server error: ${response.statusCode}',
          code: response.statusCode,
        );
    }
  }

  /// Extract error message from response
  String _extractErrorMessage(Response response) {
    try {
      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        return data['message'] ?? data['error'] ?? 'Unknown error';
      }
      return response.data?.toString() ?? 'Unknown error';
    } catch (e) {
      return 'Unknown error';
    }
  }

  /// GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// POST request
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// PUT request
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// DELETE request
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
}
