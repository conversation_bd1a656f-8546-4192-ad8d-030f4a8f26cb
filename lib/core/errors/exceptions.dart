/// Base class for all exceptions in the app
abstract class AppException implements Exception {
  final String message;
  final int? code;

  const AppException({
    required this.message,
    this.code,
  });

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'ServerException: $message (Code: $code)';
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'NetworkException: $message (Code: $code)';
}

/// Location-related exceptions
class LocationException extends AppException {
  const LocationException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'LocationException: $message (Code: $code)';
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'PermissionException: $message (Code: $code)';
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'CacheException: $message (Code: $code)';
}

/// Notification-related exceptions
class NotificationException extends AppException {
  const NotificationException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'NotificationException: $message (Code: $code)';
}

/// Audio-related exceptions
class AudioException extends AppException {
  const AudioException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'AudioException: $message (Code: $code)';
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'ValidationException: $message (Code: $code)';
}

/// Unknown exceptions
class UnknownException extends AppException {
  const UnknownException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'UnknownException: $message (Code: $code)';
}
