import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/repositories/location_repository.dart';
import '../datasources/location_local_data_source.dart';
import '../datasources/location_remote_data_source.dart';
import '../models/location_model.dart';

/// Implementation of LocationRepository
class LocationRepositoryImpl implements LocationRepository {
  final LocationRemoteDataSource remoteDataSource;
  final LocationLocalDataSource localDataSource;

  LocationRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, LocationEntity>> getCurrentLocation() async {
    try {
      final location = await remoteDataSource.getCurrentLocation();
      return Right(location);
    } on LocationException catch (e) {
      return Left(LocationFailure(message: e.message, code: e.code));
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, LocationEntity?>> getLastSavedLocation() async {
    try {
      final location = await localDataSource.getLastSavedLocation();
      return Right(location);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveLocation(LocationEntity location) async {
    try {
      final locationModel = LocationModel.fromEntity(location);
      await localDataSource.saveLocation(locationModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkLocationPermission() async {
    try {
      final hasPermission = await remoteDataSource.checkLocationPermission();
      return Right(hasPermission);
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> requestLocationPermission() async {
    try {
      final granted = await remoteDataSource.requestLocationPermission();
      return Right(granted);
    } on PermissionException catch (e) {
      return Left(PermissionFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isLocationServiceEnabled() async {
    try {
      final isEnabled = await remoteDataSource.isLocationServiceEnabled();
      return Right(isEnabled);
    } on LocationException catch (e) {
      return Left(LocationFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> openLocationSettings() async {
    try {
      await remoteDataSource.openLocationSettings();
      return const Right(null);
    } on LocationException catch (e) {
      return Left(LocationFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, LocationEntity>> getLocationFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      // For now, create a basic location entity
      // In a real app, you might use reverse geocoding API
      final location = LocationModel(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
      );
      return Right(location);
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return remoteDataSource.calculateDistance(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }
}
