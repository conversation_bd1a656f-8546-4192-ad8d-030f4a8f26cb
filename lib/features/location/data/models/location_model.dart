import '../../domain/entities/location_entity.dart';

/// Data model for location that extends the domain entity
class LocationModel extends LocationEntity {
  const LocationModel({
    required super.latitude,
    required super.longitude,
    super.city,
    super.country,
    super.address,
    required super.timestamp,
  });

  /// Create LocationModel from JSON
  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      city: json['city'] as String?,
      country: json['country'] as String?,
      address: json['address'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Convert LocationModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'country': country,
      'address': address,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create LocationModel from LocationEntity
  factory LocationModel.fromEntity(LocationEntity entity) {
    return LocationModel(
      latitude: entity.latitude,
      longitude: entity.longitude,
      city: entity.city,
      country: entity.country,
      address: entity.address,
      timestamp: entity.timestamp,
    );
  }

  /// Create LocationModel from Geolocator Position
  factory LocationModel.fromPosition(
    dynamic position, {
    String? city,
    String? country,
    String? address,
  }) {
    return LocationModel(
      latitude: position.latitude,
      longitude: position.longitude,
      city: city,
      country: country,
      address: address,
      timestamp: DateTime.now(),
    );
  }

  /// Create a copy with updated fields
  @override
  LocationModel copyWith({
    double? latitude,
    double? longitude,
    String? city,
    String? country,
    String? address,
    DateTime? timestamp,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      country: country ?? this.country,
      address: address ?? this.address,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}
