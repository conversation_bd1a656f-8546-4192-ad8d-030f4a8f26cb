import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/location_model.dart';

/// Abstract class for location remote data source
abstract class LocationRemoteDataSource {
  /// Get current device location
  Future<LocationModel> getCurrentLocation();

  /// Check if location permissions are granted
  Future<bool> checkLocationPermission();

  /// Request location permissions
  Future<bool> requestLocationPermission();

  /// Check if location services are enabled
  Future<bool> isLocationServiceEnabled();

  /// Open location settings
  Future<void> openLocationSettings();

  /// Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  );
}

/// Implementation of location remote data source using Geolocator
class LocationRemoteDataSourceImpl implements LocationRemoteDataSource {
  @override
  Future<LocationModel> getCurrentLocation() async {
    try {
      // Check if location service is enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw const LocationException(
          message: 'Location services are disabled',
        );
      }

      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw const PermissionException(
            message: 'Location permissions are denied',
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw const PermissionException(
          message: 'Location permissions are permanently denied',
        );
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationModel.fromPosition(position);
    } catch (e) {
      if (e is LocationException || e is PermissionException) {
        rethrow;
      }
      throw LocationException(
        message: 'Failed to get current location: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> checkLocationPermission() async {
    try {
      final permission = await Permission.location.status;
      return permission.isGranted;
    } catch (e) {
      throw PermissionException(
        message: 'Failed to check location permission: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> requestLocationPermission() async {
    try {
      final permission = await Permission.location.request();
      return permission.isGranted;
    } catch (e) {
      throw PermissionException(
        message: 'Failed to request location permission: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      throw LocationException(
        message: 'Failed to check location service: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      throw LocationException(
        message: 'Failed to open location settings: ${e.toString()}',
      );
    }
  }

  @override
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    ) / 1000; // Convert to kilometers
  }
}
