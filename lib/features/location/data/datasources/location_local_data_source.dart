import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/location_model.dart';

/// Abstract class for location local data source
abstract class LocationLocalDataSource {
  /// Get last saved location from local storage
  Future<LocationModel?> getLastSavedLocation();

  /// Save location to local storage
  Future<void> saveLocation(LocationModel location);

  /// Clear saved location
  Future<void> clearLocation();
}

/// Implementation of location local data source using SharedPreferences
class LocationLocalDataSourceImpl implements LocationLocalDataSource {
  final SharedPreferences sharedPreferences;

  LocationLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<LocationModel?> getLastSavedLocation() async {
    try {
      final jsonString = sharedPreferences.getString(AppConstants.locationLatKey);
      if (jsonString != null) {
        final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
        return LocationModel.fromJson(jsonMap);
      }
      return null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get saved location: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> saveLocation(LocationModel location) async {
    try {
      final jsonString = json.encode(location.toJson());
      final success = await sharedPreferences.setString(
        AppConstants.locationLatKey,
        jsonString,
      );
      
      if (!success) {
        throw const CacheException(
          message: 'Failed to save location to local storage',
        );
      }

      // Also save individual coordinates for backward compatibility
      await sharedPreferences.setDouble(
        AppConstants.locationLatKey,
        location.latitude,
      );
      await sharedPreferences.setDouble(
        AppConstants.locationLngKey,
        location.longitude,
      );
      
      if (location.city != null) {
        await sharedPreferences.setString(
          AppConstants.locationCityKey,
          location.city!,
        );
      }
    } catch (e) {
      throw CacheException(
        message: 'Failed to save location: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> clearLocation() async {
    try {
      await Future.wait([
        sharedPreferences.remove(AppConstants.locationLatKey),
        sharedPreferences.remove(AppConstants.locationLngKey),
        sharedPreferences.remove(AppConstants.locationCityKey),
      ]);
    } catch (e) {
      throw CacheException(
        message: 'Failed to clear location: ${e.toString()}',
      );
    }
  }
}
