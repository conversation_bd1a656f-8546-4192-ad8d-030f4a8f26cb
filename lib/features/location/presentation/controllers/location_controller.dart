import 'package:get/get.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/usecases/get_current_location.dart';

/// Controller for managing location state using GetX
class LocationController extends GetxController {
  final GetCurrentLocation getCurrentLocationUseCase;

  LocationController({
    required this.getCurrentLocationUseCase,
  });

  // Observable variables
  final _isLoading = false.obs;
  final _currentLocation = Rxn<LocationEntity>();
  final _errorMessage = ''.obs;
  final _hasLocationPermission = false.obs;
  final _isLocationServiceEnabled = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  LocationEntity? get currentLocation => _currentLocation.value;
  String get errorMessage => _errorMessage.value;
  bool get hasLocationPermission => _hasLocationPermission.value;
  bool get isLocationServiceEnabled => _isLocationServiceEnabled.value;

  // Check if location is available
  bool get hasLocation => _currentLocation.value != null;

  // Get formatted location string
  String get locationString {
    if (_currentLocation.value != null) {
      return _currentLocation.value!.displayName;
    }
    return 'Location not available';
  }

  // Get coordinates string
  String get coordinatesString {
    if (_currentLocation.value != null) {
      return _currentLocation.value!.coordinatesString;
    }
    return '';
  }

  @override
  void onInit() {
    super.onInit();
    // Try to get location on initialization
    getCurrentLocation();
  }

  /// Get current device location
  Future<void> getCurrentLocation() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await getCurrentLocationUseCase(NoParams());

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          _currentLocation.value = null;
          
          // Handle specific error types
          if (failure.message.contains('permission')) {
            _hasLocationPermission.value = false;
          } else if (failure.message.contains('service')) {
            _isLocationServiceEnabled.value = false;
          }
        },
        (location) {
          _currentLocation.value = location;
          _hasLocationPermission.value = true;
          _isLocationServiceEnabled.value = true;
          _errorMessage.value = '';
        },
      );
    } catch (e) {
      _errorMessage.value = 'Unexpected error: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh location
  Future<void> refreshLocation() async {
    await getCurrentLocation();
  }

  /// Clear current location
  void clearLocation() {
    _currentLocation.value = null;
    _errorMessage.value = '';
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Check if coordinates are valid
  bool isValidLocation(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return false;
    return latitude >= -90 && latitude <= 90 && 
           longitude >= -180 && longitude <= 180;
  }

  /// Set location manually (for testing or manual input)
  void setLocation(LocationEntity location) {
    _currentLocation.value = location;
    _errorMessage.value = '';
  }

  /// Get distance to a specific location
  double? getDistanceTo(double latitude, double longitude) {
    if (_currentLocation.value == null) return null;
    
    // This would use the repository's calculateDistance method
    // For now, return null as we need to inject the repository
    return null;
  }
}
