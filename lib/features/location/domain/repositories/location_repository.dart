import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/location_entity.dart';

/// Abstract repository for location-related operations
abstract class LocationRepository {
  /// Get current device location
  Future<Either<Failure, LocationEntity>> getCurrentLocation();

  /// Get last saved location from local storage
  Future<Either<Failure, LocationEntity?>> getLastSavedLocation();

  /// Save location to local storage
  Future<Either<Failure, void>> saveLocation(LocationEntity location);

  /// Check if location permissions are granted
  Future<Either<Failure, bool>> checkLocationPermission();

  /// Request location permissions
  Future<Either<Failure, bool>> requestLocationPermission();

  /// Check if location services are enabled
  Future<Either<Failure, bool>> isLocationServiceEnabled();

  /// Open location settings
  Future<Either<Failure, void>> openLocationSettings();

  /// Get location from coordinates (reverse geocoding)
  Future<Either<Failure, LocationEntity>> getLocationFromCoordinates(
    double latitude,
    double longitude,
  );

  /// Calculate distance between two locations in kilometers
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  );
}
