import 'package:equatable/equatable.dart';

/// Location entity representing geographical coordinates and address information
class LocationEntity extends Equatable {
  final double latitude;
  final double longitude;
  final String? city;
  final String? country;
  final String? address;
  final DateTime timestamp;

  const LocationEntity({
    required this.latitude,
    required this.longitude,
    this.city,
    this.country,
    this.address,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        city,
        country,
        address,
        timestamp,
      ];

  /// Create a copy of this location with updated fields
  LocationEntity copyWith({
    double? latitude,
    double? longitude,
    String? city,
    String? country,
    String? address,
    DateTime? timestamp,
  }) {
    return LocationEntity(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      country: country ?? this.country,
      address: address ?? this.address,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Check if location is valid (has valid coordinates)
  bool get isValid {
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  /// Get formatted coordinates string
  String get coordinatesString {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  /// Get display name for the location
  String get displayName {
    if (city != null && country != null) {
      return '$city, $country';
    } else if (city != null) {
      return city!;
    } else if (country != null) {
      return country!;
    } else {
      return coordinatesString;
    }
  }

  @override
  String toString() {
    return 'LocationEntity(lat: $latitude, lng: $longitude, city: $city, country: $country)';
  }
}
