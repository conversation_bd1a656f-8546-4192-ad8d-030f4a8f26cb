import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/location_entity.dart';
import '../repositories/location_repository.dart';

/// Use case for getting current device location
class GetCurrentLocation implements UseCase<LocationEntity, NoParams> {
  final LocationRepository repository;

  GetCurrentLocation(this.repository);

  @override
  Future<Either<Failure, LocationEntity>> call(NoParams params) async {
    // First check if location permission is granted
    final permissionResult = await repository.checkLocationPermission();
    
    return permissionResult.fold(
      (failure) => Left(failure),
      (hasPermission) async {
        if (!hasPermission) {
          // Try to request permission
          final requestResult = await repository.requestLocationPermission();
          return requestResult.fold(
            (failure) => Left(failure),
            (granted) async {
              if (!granted) {
                return const Left(PermissionFailure(
                  message: 'Location permission denied by user',
                ));
              }
              return _getCurrentLocation();
            },
          );
        }
        return _getCurrentLocation();
      },
    );
  }

  Future<Either<Failure, LocationEntity>> _getCurrentLocation() async {
    // Check if location service is enabled
    final serviceResult = await repository.isLocationServiceEnabled();
    
    return serviceResult.fold(
      (failure) => Left(failure),
      (isEnabled) async {
        if (!isEnabled) {
          return const Left(LocationFailure(
            message: 'Location service is disabled',
          ));
        }
        
        // Get current location
        final locationResult = await repository.getCurrentLocation();
        
        return locationResult.fold(
          (failure) => Left(failure),
          (location) async {
            // Save location for future use
            await repository.saveLocation(location);
            return Right(location);
          },
        );
      },
    );
  }
}
