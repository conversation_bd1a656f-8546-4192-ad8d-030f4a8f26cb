import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/prayer_times_entity.dart';

/// Abstract repository for prayer times operations
abstract class PrayerTimesRepository {
  /// Get prayer times for a specific date and location
  Future<Either<Failure, PrayerTimesEntity>> getPrayerTimes({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
  });

  /// Get prayer times for current date and location
  Future<Either<Failure, PrayerTimesEntity>> getTodayPrayerTimes({
    required double latitude,
    required double longitude,
    int? calculationMethod,
  });

  /// Get prayer times for multiple days
  Future<Either<Failure, List<PrayerTimesEntity>>> getPrayerTimesForDateRange({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
    int? calculationMethod,
  });

  /// Get cached prayer times from local storage
  Future<Either<Failure, PrayerTimesEntity?>> getCachedPrayerTimes(DateTime date);

  /// Cache prayer times to local storage
  Future<Either<Failure, void>> cachePrayerTimes(PrayerTimesEntity prayerTimes);

  /// Clear cached prayer times
  Future<Either<Failure, void>> clearCachedPrayerTimes();

  /// Check if prayer times are cached for a specific date
  Future<Either<Failure, bool>> isPrayerTimesCached(DateTime date);

  /// Get prayer times with automatic caching
  Future<Either<Failure, PrayerTimesEntity>> getPrayerTimesWithCache({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
    bool forceRefresh = false,
  });
}
