import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/prayer_times_entity.dart';
import '../repositories/prayer_times_repository.dart';

/// Use case for getting prayer times
class GetPrayerTimes implements UseCase<PrayerTimesEntity, GetPrayerTimesParams> {
  final PrayerTimesRepository repository;

  GetPrayerTimes(this.repository);

  @override
  Future<Either<Failure, PrayerTimesEntity>> call(GetPrayerTimesParams params) async {
    return await repository.getPrayerTimesWithCache(
      latitude: params.latitude,
      longitude: params.longitude,
      date: params.date,
      calculationMethod: params.calculationMethod,
      forceRefresh: params.forceRefresh,
    );
  }
}

/// Parameters for GetPrayerTimes use case
class GetPrayerTimesParams extends Equatable {
  final double latitude;
  final double longitude;
  final DateTime date;
  final int? calculationMethod;
  final bool forceRefresh;

  const GetPrayerTimesParams({
    required this.latitude,
    required this.longitude,
    required this.date,
    this.calculationMethod,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        date,
        calculationMethod,
        forceRefresh,
      ];
}
