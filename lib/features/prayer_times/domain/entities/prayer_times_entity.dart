import 'package:equatable/equatable.dart';

/// Enum for different prayer types
enum PrayerType {
  fajr('الفجر', 'Fajr'),
  dhuhr('الظهر', '<PERSON>huhr'),
  asr('العصر', 'Asr'),
  maghrib('المغرب', 'Maghrib'),
  isha('العشاء', 'Isha');

  const PrayerType(this.arabicName, this.englishName);
  
  final String arabicName;
  final String englishName;
}

/// Entity representing prayer times for a specific date
class PrayerTimesEntity extends Equatable {
  final DateTime date;
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime sunset;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime midnight;
  final String hijriDate;
  final String location;

  const PrayerTimesEntity({
    required this.date,
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.sunset,
    required this.maghrib,
    required this.isha,
    required this.midnight,
    required this.hijriDate,
    required this.location,
  });

  @override
  List<Object?> get props => [
        date,
        fajr,
        sunrise,
        dhuhr,
        asr,
        sunset,
        maghrib,
        isha,
        midnight,
        hijriDate,
        location,
      ];

  /// Get prayer time by prayer type
  DateTime getPrayerTime(PrayerType prayerType) {
    switch (prayerType) {
      case PrayerType.fajr:
        return fajr;
      case PrayerType.dhuhr:
        return dhuhr;
      case PrayerType.asr:
        return asr;
      case PrayerType.maghrib:
        return maghrib;
      case PrayerType.isha:
        return isha;
    }
  }

  /// Get all prayer times as a list
  List<DateTime> get allPrayerTimes => [fajr, dhuhr, asr, maghrib, isha];

  /// Get prayer times with their types
  Map<PrayerType, DateTime> get prayerTimesMap => {
        PrayerType.fajr: fajr,
        PrayerType.dhuhr: dhuhr,
        PrayerType.asr: asr,
        PrayerType.maghrib: maghrib,
        PrayerType.isha: isha,
      };

  /// Get next prayer from current time
  PrayerType? getNextPrayer([DateTime? currentTime]) {
    final now = currentTime ?? DateTime.now();
    
    for (final prayerType in PrayerType.values) {
      final prayerTime = getPrayerTime(prayerType);
      if (prayerTime.isAfter(now)) {
        return prayerType;
      }
    }
    
    // If all prayers have passed, next prayer is Fajr of next day
    return PrayerType.fajr;
  }

  /// Get current prayer (the last prayer that has passed)
  PrayerType? getCurrentPrayer([DateTime? currentTime]) {
    final now = currentTime ?? DateTime.now();
    PrayerType? currentPrayer;
    
    for (final prayerType in PrayerType.values) {
      final prayerTime = getPrayerTime(prayerType);
      if (prayerTime.isBefore(now) || prayerTime.isAtSameMomentAs(now)) {
        currentPrayer = prayerType;
      } else {
        break;
      }
    }
    
    return currentPrayer;
  }

  /// Get time remaining until next prayer
  Duration? getTimeUntilNextPrayer([DateTime? currentTime]) {
    final now = currentTime ?? DateTime.now();
    final nextPrayer = getNextPrayer(now);
    
    if (nextPrayer == null) return null;
    
    DateTime nextPrayerTime;
    if (nextPrayer == PrayerType.fajr && 
        getPrayerTime(PrayerType.fajr).isBefore(now)) {
      // Next Fajr is tomorrow, we need to add 24 hours
      nextPrayerTime = getPrayerTime(PrayerType.fajr).add(const Duration(days: 1));
    } else {
      nextPrayerTime = getPrayerTime(nextPrayer);
    }
    
    return nextPrayerTime.difference(now);
  }

  /// Check if it's currently prayer time (within 15 minutes)
  bool isCurrentlyPrayerTime([DateTime? currentTime]) {
    final now = currentTime ?? DateTime.now();
    
    for (final prayerType in PrayerType.values) {
      final prayerTime = getPrayerTime(prayerType);
      final timeDifference = now.difference(prayerTime).abs();
      
      if (timeDifference.inMinutes <= 15) {
        return true;
      }
    }
    
    return false;
  }

  /// Get formatted date string
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Copy with updated fields
  PrayerTimesEntity copyWith({
    DateTime? date,
    DateTime? fajr,
    DateTime? sunrise,
    DateTime? dhuhr,
    DateTime? asr,
    DateTime? sunset,
    DateTime? maghrib,
    DateTime? isha,
    DateTime? midnight,
    String? hijriDate,
    String? location,
  }) {
    return PrayerTimesEntity(
      date: date ?? this.date,
      fajr: fajr ?? this.fajr,
      sunrise: sunrise ?? this.sunrise,
      dhuhr: dhuhr ?? this.dhuhr,
      asr: asr ?? this.asr,
      sunset: sunset ?? this.sunset,
      maghrib: maghrib ?? this.maghrib,
      isha: isha ?? this.isha,
      midnight: midnight ?? this.midnight,
      hijriDate: hijriDate ?? this.hijriDate,
      location: location ?? this.location,
    );
  }

  @override
  String toString() {
    return 'PrayerTimesEntity(date: $date, location: $location, fajr: $fajr, dhuhr: $dhuhr, asr: $asr, maghrib: $maghrib, isha: $isha)';
  }
}
