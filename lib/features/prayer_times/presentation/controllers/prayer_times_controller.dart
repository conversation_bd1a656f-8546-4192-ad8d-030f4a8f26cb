import 'dart:async';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../core/controllers/base_controller.dart';
import '../../domain/entities/prayer_times_entity.dart';
import '../../domain/usecases/get_prayer_times.dart';
import '../../../location/presentation/controllers/location_controller.dart';

/// Controller for managing prayer times state using GetX
class PrayerTimesController extends BaseController {
  final GetPrayerTimes getPrayerTimesUseCase;

  PrayerTimesController({
    required this.getPrayerTimesUseCase,
  });

  // Observable variables
  final _prayerTimes = Rxn<PrayerTimesEntity>();
  final _nextPrayer = Rxn<PrayerType>();
  final _currentPrayer = Rxn<PrayerType>();
  final _timeUntilNextPrayer = Rxn<Duration>();
  final _currentTime = DateTime.now().obs;
  final _isAutoRefreshEnabled = true.obs;

  // Timer for updating countdown
  Timer? _countdownTimer;
  Timer? _dailyRefreshTimer;

  // Getters
  PrayerTimesEntity? get prayerTimes => _prayerTimes.value;
  PrayerType? get nextPrayer => _nextPrayer.value;
  PrayerType? get currentPrayer => _currentPrayer.value;
  Duration? get timeUntilNextPrayer => _timeUntilNextPrayer.value;
  DateTime get currentTime => _currentTime.value;
  bool get isAutoRefreshEnabled => _isAutoRefreshEnabled.value;

  // Check if prayer times are available
  bool get hasPrayerTimes => _prayerTimes.value != null;

  // Get formatted time strings
  String get nextPrayerName {
    if (_nextPrayer.value != null) {
      return _nextPrayer.value!.arabicName;
    }
    return '';
  }

  String get timeUntilNextPrayerString {
    if (_timeUntilNextPrayer.value != null) {
      final duration = _timeUntilNextPrayer.value!;
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      final seconds = duration.inSeconds % 60;
      
      if (hours > 0) {
        return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      } else {
        return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      }
    }
    return '';
  }

  String get currentTimeString {
    return DateFormat('HH:mm:ss').format(_currentTime.value);
  }

  String get currentDateString {
    return DateFormat('EEEE، dd MMMM yyyy', 'ar').format(_currentTime.value);
  }

  @override
  void onInit() {
    super.onInit();
    _startTimers();
    _loadPrayerTimes();
  }

  @override
  void onClose() {
    _stopTimers();
    super.onClose();
  }

  /// Load prayer times for today
  Future<void> _loadPrayerTimes() async {
    final locationController = Get.find<LocationController>();
    
    if (!locationController.hasLocation) {
      setError('الموقع غير متوفر. يرجى تحديد الموقع أولاً.');
      return;
    }

    await handleAsyncOperation(
      () async {
        final location = locationController.currentLocation!;
        
        final result = await getPrayerTimesUseCase(
          GetPrayerTimesParams(
            latitude: location.latitude,
            longitude: location.longitude,
            date: DateTime.now(),
          ),
        );

        result.fold(
          (failure) {
            throw Exception(failure.message);
          },
          (prayerTimes) {
            _prayerTimes.value = prayerTimes;
            _updatePrayerStatus();
          },
        );
      },
      loadingMessage: 'جاري تحميل أوقات الصلاة...',
      successMessage: 'تم تحميل أوقات الصلاة بنجاح',
    );
  }

  /// Refresh prayer times
  Future<void> refreshPrayerTimes({bool forceRefresh = false}) async {
    final locationController = Get.find<LocationController>();
    
    if (!locationController.hasLocation) {
      setError('الموقع غير متوفر. يرجى تحديد الموقع أولاً.');
      return;
    }

    await handleAsyncOperation(
      () async {
        final location = locationController.currentLocation!;
        
        final result = await getPrayerTimesUseCase(
          GetPrayerTimesParams(
            latitude: location.latitude,
            longitude: location.longitude,
            date: DateTime.now(),
            forceRefresh: forceRefresh,
          ),
        );

        result.fold(
          (failure) {
            throw Exception(failure.message);
          },
          (prayerTimes) {
            _prayerTimes.value = prayerTimes;
            _updatePrayerStatus();
          },
        );
      },
      showLoading: false,
      successMessage: 'تم تحديث أوقات الصلاة',
    );
  }

  /// Update prayer status (next prayer, current prayer, countdown)
  void _updatePrayerStatus() {
    if (_prayerTimes.value == null) return;

    final now = DateTime.now();
    _currentTime.value = now;

    _nextPrayer.value = _prayerTimes.value!.getNextPrayer(now);
    _currentPrayer.value = _prayerTimes.value!.getCurrentPrayer(now);
    _timeUntilNextPrayer.value = _prayerTimes.value!.getTimeUntilNextPrayer(now);
  }

  /// Start timers for countdown and daily refresh
  void _startTimers() {
    // Update countdown every second
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updatePrayerStatus();
    });

    // Refresh prayer times daily at midnight
    _dailyRefreshTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      final now = DateTime.now();
      if (now.hour == 0 && now.minute == 0) {
        _loadPrayerTimes();
      }
    });
  }

  /// Stop all timers
  void _stopTimers() {
    _countdownTimer?.cancel();
    _dailyRefreshTimer?.cancel();
  }

  /// Toggle auto refresh
  void toggleAutoRefresh() {
    _isAutoRefreshEnabled.value = !_isAutoRefreshEnabled.value;
    
    if (_isAutoRefreshEnabled.value) {
      _startTimers();
    } else {
      _stopTimers();
    }
  }

  /// Get prayer time string for a specific prayer
  String getPrayerTimeString(PrayerType prayerType) {
    if (_prayerTimes.value == null) return '';
    
    final prayerTime = _prayerTimes.value!.getPrayerTime(prayerType);
    return DateFormat('HH:mm').format(prayerTime);
  }

  /// Check if it's currently prayer time
  bool get isCurrentlyPrayerTime {
    return _prayerTimes.value?.isCurrentlyPrayerTime() ?? false;
  }

  /// Get time remaining until a specific prayer
  Duration? getTimeUntilPrayer(PrayerType prayerType) {
    if (_prayerTimes.value == null) return null;
    
    final prayerTime = _prayerTimes.value!.getPrayerTime(prayerType);
    final now = DateTime.now();
    
    if (prayerTime.isAfter(now)) {
      return prayerTime.difference(now);
    }
    
    return null;
  }

  /// Format duration to readable string
  String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }
}
