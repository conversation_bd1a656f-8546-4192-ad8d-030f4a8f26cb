import 'package:intl/intl.dart';
import '../../domain/entities/prayer_times_entity.dart';

/// Data model for prayer times that extends the domain entity
class PrayerTimesModel extends PrayerTimesEntity {
  const PrayerTimesModel({
    required super.date,
    required super.fajr,
    required super.sunrise,
    required super.dhuhr,
    required super.asr,
    required super.sunset,
    required super.maghrib,
    required super.isha,
    required super.midnight,
    required super.hijriDate,
    required super.location,
  });

  /// Create PrayerTimesModel from Aladhan API JSON response
  factory PrayerTimesModel.fromAladhanJson(Map<String, dynamic> json) {
    final timings = json['data']['timings'] as Map<String, dynamic>;
    final dateInfo = json['data']['date'] as Map<String, dynamic>;
    final gregorian = dateInfo['gregorian'] as Map<String, dynamic>;
    final hijri = dateInfo['hijri'] as Map<String, dynamic>;
    
    // Parse the date
    final dateString = gregorian['date'] as String;
    final date = DateFormat('dd-MM-yyyy').parse(dateString);
    
    // Helper function to parse time and create DateTime
    DateTime parseTime(String timeString) {
      // Remove timezone info if present (e.g., "05:30 (+03)" -> "05:30")
      final cleanTime = timeString.split(' ')[0];
      final timeParts = cleanTime.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);
      
      return DateTime(date.year, date.month, date.day, hour, minute);
    }

    // Parse all prayer times
    final fajr = parseTime(timings['Fajr'] as String);
    final sunrise = parseTime(timings['Sunrise'] as String);
    final dhuhr = parseTime(timings['Dhuhr'] as String);
    final asr = parseTime(timings['Asr'] as String);
    final sunset = parseTime(timings['Sunset'] as String);
    final maghrib = parseTime(timings['Maghrib'] as String);
    final isha = parseTime(timings['Isha'] as String);
    final midnight = parseTime(timings['Midnight'] as String);

    // Create Hijri date string
    final hijriDate = '${hijri['day']} ${hijri['month']['ar']} ${hijri['year']}';
    
    // Create location string
    final meta = json['data']['meta'] as Map<String, dynamic>?;
    final location = meta?['timezone'] as String? ?? 'Unknown Location';

    return PrayerTimesModel(
      date: date,
      fajr: fajr,
      sunrise: sunrise,
      dhuhr: dhuhr,
      asr: asr,
      sunset: sunset,
      maghrib: maghrib,
      isha: isha,
      midnight: midnight,
      hijriDate: hijriDate,
      location: location,
    );
  }

  /// Create PrayerTimesModel from local JSON storage
  factory PrayerTimesModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimesModel(
      date: DateTime.parse(json['date'] as String),
      fajr: DateTime.parse(json['fajr'] as String),
      sunrise: DateTime.parse(json['sunrise'] as String),
      dhuhr: DateTime.parse(json['dhuhr'] as String),
      asr: DateTime.parse(json['asr'] as String),
      sunset: DateTime.parse(json['sunset'] as String),
      maghrib: DateTime.parse(json['maghrib'] as String),
      isha: DateTime.parse(json['isha'] as String),
      midnight: DateTime.parse(json['midnight'] as String),
      hijriDate: json['hijriDate'] as String,
      location: json['location'] as String,
    );
  }

  /// Convert PrayerTimesModel to JSON for local storage
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'fajr': fajr.toIso8601String(),
      'sunrise': sunrise.toIso8601String(),
      'dhuhr': dhuhr.toIso8601String(),
      'asr': asr.toIso8601String(),
      'sunset': sunset.toIso8601String(),
      'maghrib': maghrib.toIso8601String(),
      'isha': isha.toIso8601String(),
      'midnight': midnight.toIso8601String(),
      'hijriDate': hijriDate,
      'location': location,
    };
  }

  /// Create PrayerTimesModel from PrayerTimesEntity
  factory PrayerTimesModel.fromEntity(PrayerTimesEntity entity) {
    return PrayerTimesModel(
      date: entity.date,
      fajr: entity.fajr,
      sunrise: entity.sunrise,
      dhuhr: entity.dhuhr,
      asr: entity.asr,
      sunset: entity.sunset,
      maghrib: entity.maghrib,
      isha: entity.isha,
      midnight: entity.midnight,
      hijriDate: entity.hijriDate,
      location: entity.location,
    );
  }

  /// Create a copy with updated fields
  @override
  PrayerTimesModel copyWith({
    DateTime? date,
    DateTime? fajr,
    DateTime? sunrise,
    DateTime? dhuhr,
    DateTime? asr,
    DateTime? sunset,
    DateTime? maghrib,
    DateTime? isha,
    DateTime? midnight,
    String? hijriDate,
    String? location,
  }) {
    return PrayerTimesModel(
      date: date ?? this.date,
      fajr: fajr ?? this.fajr,
      sunrise: sunrise ?? this.sunrise,
      dhuhr: dhuhr ?? this.dhuhr,
      asr: asr ?? this.asr,
      sunset: sunset ?? this.sunset,
      maghrib: maghrib ?? this.maghrib,
      isha: isha ?? this.isha,
      midnight: midnight ?? this.midnight,
      hijriDate: hijriDate ?? this.hijriDate,
      location: location ?? this.location,
    );
  }
}
