import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../models/prayer_times_model.dart';

/// Abstract class for prayer times remote data source
abstract class PrayerTimesRemoteDataSource {
  /// Get prayer times from Aladhan API
  Future<PrayerTimesModel> getPrayerTimes({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
  });

  /// Get prayer times for current date
  Future<PrayerTimesModel> getTodayPrayerTimes({
    required double latitude,
    required double longitude,
    int? calculationMethod,
  });
}

/// Implementation of prayer times remote data source using Aladhan API
class PrayerTimesRemoteDataSourceImpl implements PrayerTimesRemoteDataSource {
  final DioClient dioClient;

  PrayerTimesRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<PrayerTimesModel> getPrayerTimes({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
  }) async {
    try {
      // Format date for API (DD-MM-YYYY)
      final formattedDate = DateFormat('dd-MM-yyyy').format(date);
      
      // Prepare query parameters
      final queryParameters = {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'date': formattedDate,
        'method': (calculationMethod ?? AppConstants.defaultCalculationMethod).toString(),
        'tune': '0,0,0,0,0,0,0,0,0', // No adjustments
      };

      // Make API call
      final response = await dioClient.get(
        AppConstants.prayerTimesEndpoint,
        queryParameters: queryParameters,
      );

      // Check if response is successful
      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Check API response status
        if (responseData['code'] == 200 && responseData['status'] == 'OK') {
          return PrayerTimesModel.fromAladhanJson(responseData);
        } else {
          throw ServerException(
            message: 'API returned error: ${responseData['status']}',
            code: responseData['code'] as int?,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get prayer times',
          code: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to get prayer times: ${e.toString()}',
      );
    }
  }

  @override
  Future<PrayerTimesModel> getTodayPrayerTimes({
    required double latitude,
    required double longitude,
    int? calculationMethod,
  }) async {
    return await getPrayerTimes(
      latitude: latitude,
      longitude: longitude,
      date: DateTime.now(),
      calculationMethod: calculationMethod,
    );
  }
}
