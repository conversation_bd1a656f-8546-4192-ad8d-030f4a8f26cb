import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/prayer_times_model.dart';

/// Abstract class for prayer times local data source
abstract class PrayerTimesLocalDataSource {
  /// Get cached prayer times for a specific date
  Future<PrayerTimesModel?> getCachedPrayerTimes(DateTime date);

  /// Cache prayer times for a specific date
  Future<void> cachePrayerTimes(PrayerTimesModel prayerTimes);

  /// Clear all cached prayer times
  Future<void> clearCachedPrayerTimes();

  /// Check if prayer times are cached for a specific date
  Future<bool> isPrayerTimesCached(DateTime date);

  /// Get last update timestamp
  Future<DateTime?> getLastUpdateTime();

  /// Set last update timestamp
  Future<void> setLastUpdateTime(DateTime dateTime);
}

/// Implementation of prayer times local data source using SharedPreferences
class PrayerTimesLocalDataSourceImpl implements PrayerTimesLocalDataSource {
  final SharedPreferences sharedPreferences;

  PrayerTimesLocalDataSourceImpl({required this.sharedPreferences});

  /// Generate cache key for a specific date
  String _getCacheKey(DateTime date) {
    final dateString = DateFormat('yyyy-MM-dd').format(date);
    return '${AppConstants.prayerTimesKey}_$dateString';
  }

  @override
  Future<PrayerTimesModel?> getCachedPrayerTimes(DateTime date) async {
    try {
      final cacheKey = _getCacheKey(date);
      final jsonString = sharedPreferences.getString(cacheKey);
      
      if (jsonString != null) {
        final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
        return PrayerTimesModel.fromJson(jsonMap);
      }
      
      return null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached prayer times: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> cachePrayerTimes(PrayerTimesModel prayerTimes) async {
    try {
      final cacheKey = _getCacheKey(prayerTimes.date);
      final jsonString = json.encode(prayerTimes.toJson());
      
      final success = await sharedPreferences.setString(cacheKey, jsonString);
      
      if (!success) {
        throw const CacheException(
          message: 'Failed to cache prayer times',
        );
      }

      // Update last update time
      await setLastUpdateTime(DateTime.now());
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache prayer times: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> clearCachedPrayerTimes() async {
    try {
      final keys = sharedPreferences.getKeys();
      final prayerTimesKeys = keys
          .where((key) => key.startsWith(AppConstants.prayerTimesKey))
          .toList();

      for (final key in prayerTimesKeys) {
        await sharedPreferences.remove(key);
      }

      // Clear last update time
      await sharedPreferences.remove(AppConstants.lastUpdateKey);
    } catch (e) {
      throw CacheException(
        message: 'Failed to clear cached prayer times: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> isPrayerTimesCached(DateTime date) async {
    try {
      final cacheKey = _getCacheKey(date);
      return sharedPreferences.containsKey(cacheKey);
    } catch (e) {
      throw CacheException(
        message: 'Failed to check cached prayer times: ${e.toString()}',
      );
    }
  }

  @override
  Future<DateTime?> getLastUpdateTime() async {
    try {
      final timestampString = sharedPreferences.getString(AppConstants.lastUpdateKey);
      
      if (timestampString != null) {
        return DateTime.parse(timestampString);
      }
      
      return null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get last update time: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> setLastUpdateTime(DateTime dateTime) async {
    try {
      final success = await sharedPreferences.setString(
        AppConstants.lastUpdateKey,
        dateTime.toIso8601String(),
      );
      
      if (!success) {
        throw const CacheException(
          message: 'Failed to set last update time',
        );
      }
    } catch (e) {
      throw CacheException(
        message: 'Failed to set last update time: ${e.toString()}',
      );
    }
  }
}
