import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/prayer_times_entity.dart';
import '../../domain/repositories/prayer_times_repository.dart';
import '../datasources/prayer_times_local_data_source.dart';
import '../datasources/prayer_times_remote_data_source.dart';
import '../models/prayer_times_model.dart';

/// Implementation of PrayerTimesRepository
class PrayerTimesRepositoryImpl implements PrayerTimesRepository {
  final PrayerTimesRemoteDataSource remoteDataSource;
  final PrayerTimesLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  PrayerTimesRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, PrayerTimesEntity>> getPrayerTimes({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final prayerTimes = await remoteDataSource.getPrayerTimes(
          latitude: latitude,
          longitude: longitude,
          date: date,
          calculationMethod: calculationMethod,
        );
        
        // Cache the result
        await localDataSource.cachePrayerTimes(prayerTimes);
        
        return Right(prayerTimes);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message, code: e.code));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(message: e.message, code: e.code));
      } catch (e) {
        return Left(UnknownFailure(message: e.toString()));
      }
    } else {
      // No internet connection, try to get from cache
      try {
        final cachedPrayerTimes = await localDataSource.getCachedPrayerTimes(date);
        if (cachedPrayerTimes != null) {
          return Right(cachedPrayerTimes);
        } else {
          return const Left(NetworkFailure(
            message: 'No internet connection and no cached data available',
          ));
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message, code: e.code));
      }
    }
  }

  @override
  Future<Either<Failure, PrayerTimesEntity>> getTodayPrayerTimes({
    required double latitude,
    required double longitude,
    int? calculationMethod,
  }) async {
    return await getPrayerTimes(
      latitude: latitude,
      longitude: longitude,
      date: DateTime.now(),
      calculationMethod: calculationMethod,
    );
  }

  @override
  Future<Either<Failure, List<PrayerTimesEntity>>> getPrayerTimesForDateRange({
    required double latitude,
    required double longitude,
    required DateTime startDate,
    required DateTime endDate,
    int? calculationMethod,
  }) async {
    try {
      final List<PrayerTimesEntity> prayerTimesList = [];
      DateTime currentDate = startDate;

      while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
        final result = await getPrayerTimes(
          latitude: latitude,
          longitude: longitude,
          date: currentDate,
          calculationMethod: calculationMethod,
        );

        result.fold(
          (failure) => throw Exception(failure.message),
          (prayerTimes) => prayerTimesList.add(prayerTimes),
        );

        currentDate = currentDate.add(const Duration(days: 1));
      }

      return Right(prayerTimesList);
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PrayerTimesEntity?>> getCachedPrayerTimes(DateTime date) async {
    try {
      final cachedPrayerTimes = await localDataSource.getCachedPrayerTimes(date);
      return Right(cachedPrayerTimes);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cachePrayerTimes(PrayerTimesEntity prayerTimes) async {
    try {
      final prayerTimesModel = PrayerTimesModel.fromEntity(prayerTimes);
      await localDataSource.cachePrayerTimes(prayerTimesModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearCachedPrayerTimes() async {
    try {
      await localDataSource.clearCachedPrayerTimes();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isPrayerTimesCached(DateTime date) async {
    try {
      final isCached = await localDataSource.isPrayerTimesCached(date);
      return Right(isCached);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PrayerTimesEntity>> getPrayerTimesWithCache({
    required double latitude,
    required double longitude,
    required DateTime date,
    int? calculationMethod,
    bool forceRefresh = false,
  }) async {
    // If force refresh is not requested, try to get from cache first
    if (!forceRefresh) {
      final cachedResult = await getCachedPrayerTimes(date);
      final cachedPrayerTimes = cachedResult.fold(
        (failure) => null,
        (prayerTimes) => prayerTimes,
      );

      if (cachedPrayerTimes != null) {
        return Right(cachedPrayerTimes);
      }
    }

    // Get from remote source
    return await getPrayerTimes(
      latitude: latitude,
      longitude: longitude,
      date: date,
      calculationMethod: calculationMethod,
    );
  }
}
