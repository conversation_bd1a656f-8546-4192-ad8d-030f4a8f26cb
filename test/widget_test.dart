// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:salati/core/constants/app_constants.dart';

void main() {
  testWidgets('Salati app smoke test', (WidgetTester tester) async {
    // Build a simple test app without dependencies
    await tester.pumpWidget(
      GetMaterialApp(
        title: AppConstants.appName,
        home: Scaffold(
          appBar: AppBar(
            title: const Text(AppConstants.appName),
          ),
          body: const Center(
            child: Text('Test App'),
          ),
        ),
      ),
    );

    // Verify that the app title is displayed
    expect(find.text('صلاتي - Salati'), findsOneWidget);

    // Verify that test content is displayed
    expect(find.text('Test App'), findsOneWidget);
  });
}
